---
marp: true
theme: default
class: lead
paginate: true
backgroundColor: #ffffff
backgroundImage: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
color: #ffffff
style: |
  .columns {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem;
  }
  .feature-box {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin: 8px 0;
    backdrop-filter: blur(10px);
  }
  .highlight {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
  }
  .stats {
    font-size: 2em;
    font-weight: bold;
    color: #ffd700;
    text-align: center;
  }
  h1 { font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5em; }
  h2 { font-size: 1.8em; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); margin-bottom: 0.5em; }
  h3 { font-size: 1.3em; margin-bottom: 0.3em; }
  .quote {
    font-style: italic;
    border-left: 4px solid #ffd700;
    padding: 12px;
    margin: 15px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    font-size: 0.9em;
  }
  ul { font-size: 0.9em; }
  p { font-size: 0.95em; line-height: 1.4; }
  a { color: #ffffff !important; text-decoration: underline; }
  a:hover { color: #ffd700 !important; }
---

# 🎨 Marp幻灯片通用模板

## 适配16:9比例的精美设计

**请在此处添加您的内容**

---

# 📋 模板说明

## 🎯 设计特色
- **渐变背景**：紫色渐变营造专业感
- **双栏布局**：充分利用16:9横向空间
- **视觉层次**：清晰的标题和内容层级
- **响应式设计**：适配不同内容长度

## 🔧 可用组件

### 双栏布局
```markdown
<div class="columns">
<div>
左栏内容
</div>
<div>
右栏内容
</div>
</div>
```

---

# 📦 组件示例

<div class="columns">
<div>

### 🎁 特性框示例
<div class="feature-box">

## 📊 数据展示
**重要信息**突出显示
- 列表项目1
- 列表项目2
- 列表项目3

</div>

</div>
<div>

### 💬 引用框示例
<div class="quote">
"这是一个引用示例，展示如何使用引用样式来突出重要的话语或评价。"
<br><br>
<strong>— 引用来源</strong>
</div>

</div>
</div>

---

# 🎨 样式组件

## 📈 统计数字
<div class="stats">

**15x**
增长倍数

**100+**
用户数量

**2.5年**
研发时间

</div>

## 🌟 高亮文字
<div class="highlight">

这是高亮文字效果，使用渐变色彩

</div>

---

# 📝 使用说明

## 🔧 如何使用此模板

1. **复制CSS样式**：将开头的CSS配置复制到新文件
2. **替换内容**：删除示例内容，添加您的实际内容
3. **调整布局**：根据内容长度选择单栏或双栏布局
4. **优化文字**：确保内容适配16:9页面比例

## ⚠️ 注意事项

- 保持文字简洁，避免内容溢出
- 合理使用双栏布局分配内容
- 链接会自动显示为白色，确保可读性
- 可以自由调整渐变背景颜色

---

# 🎉 开始创作

**现在您可以使用这个模板创建精美的幻灯片了！**

只需：
1. 复制此模板
2. 替换为您的内容
3. 享受专业的视觉效果

**祝您创作愉快！** ✨
