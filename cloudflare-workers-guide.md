---
marp: true
theme: default
class: lead
paginate: true
backgroundColor: #ffffff
backgroundImage: linear-gradient(135deg, #667eea 0%, #764ba2 100%)
color: #ffffff
style: |
  .columns {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem;
  }
  .feature-box {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin: 8px 0;
    backdrop-filter: blur(10px);
  }
  .highlight {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
  }
  .stats {
    font-size: 2em;
    font-weight: bold;
    color: #ffd700;
    text-align: center;
  }
  h1 { font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); margin-bottom: 0.5em; }
  h2 { font-size: 1.8em; text-shadow: 1px 1px 2px rgba(0,0,0,0.3); margin-bottom: 0.5em; }
  h3 { font-size: 1.3em; margin-bottom: 0.3em; }
  .quote {
    font-style: italic;
    border-left: 4px solid #ffd700;
    padding: 12px;
    margin: 15px 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    font-size: 0.9em;
  }
  ul { font-size: 0.9em; }
  p { font-size: 0.95em; line-height: 1.4; }
  a { color: #ffffff !important; text-decoration: underline; }
  a:hover { color: #ffd700 !important; }
---

# ☁️ Cloudflare Workers 使用指南

## 边缘计算的强大力量

**从零开始掌握无服务器边缘计算**

---

# 🌟 什么是 Cloudflare Workers？

<div class="columns">
<div>

## 🚀 核心概念
<div class="feature-box">

**无服务器边缘计算平台**
- 在全球200+数据中心运行
- 毫秒级冷启动时间
- 基于V8 JavaScript引擎
- 支持多种编程语言

</div>

</div>
<div>

## 📊 性能优势
<div class="stats">

**0ms**
冷启动时间

**200+**
全球节点

**100ms**
平均响应时间

</div>

</div>
</div>

---

# 🎯 主要特性与优势

<div class="columns">
<div>

### ⚡ 性能特性
<div class="feature-box">

- **边缘计算**：就近处理请求
- **零冷启动**：即时响应
- **自动扩展**：无需配置
- **全球分发**：200+数据中心

</div>

### 💰 成本优势
<div class="feature-box">

- **按需付费**：只为使用付费
- **免费额度**：每日100,000次请求
- **无基础设施成本**
- **自动优化**：无需运维

</div>

</div>
<div>

### 🔧 开发体验
<div class="feature-box">

- **简单部署**：一键发布
- **实时日志**：即时调试
- **版本控制**：回滚支持
- **本地开发**：Wrangler CLI

</div>

### 🌐 应用场景
<div class="feature-box">

- **API网关**：请求路由和处理
- **静态网站**：CDN加速
- **A/B测试**：流量分割
- **安全防护**：WAF规则

</div>

</div>
</div>

---

# 🚀 快速开始

## 第一步：环境准备

<div class="columns">
<div>

### 📦 安装 Wrangler CLI
```bash
npm install -g wrangler

# 或使用 yarn
yarn global add wrangler

# 验证安装
wrangler --version
```

### 🔑 登录认证
```bash
wrangler login
```

</div>
<div>

### 🆕 创建新项目
```bash
# 创建新Worker
wrangler generate my-worker

# 进入项目目录
cd my-worker

# 查看项目结构
ls -la
```

### 📁 项目结构
```
my-worker/
├── src/
│   └── index.js
├── wrangler.toml
└── package.json
```

</div>
</div>

---

# 💻 基础代码示例

## Hello World Worker

<div class="columns">
<div>

### 🎯 基础处理器
```javascript
export default {
  async fetch(request, env, ctx) {
    return new Response(
      'Hello Cloudflare Workers!',
      {
        headers: {
          'content-type': 'text/plain',
        },
      }
    );
  },
};
```

</div>
<div>

### 🌐 HTTP 方法处理
```javascript
export default {
  async fetch(request, env, ctx) {
    const { method, url } = request;
    const { pathname } = new URL(url);
    
    if (method === 'GET' && pathname === '/') {
      return new Response('GET request');
    }
    
    if (method === 'POST' && pathname === '/api') {
      const body = await request.json();
      return Response.json({ 
        message: 'POST received',
        data: body 
      });
    }
    
    return new Response('Not Found', { 
      status: 404 
    });
  },
};
```

</div>
</div>

---

# 🔧 高级功能

<div class="columns">
<div>

### 🗄️ KV 存储
```javascript
export default {
  async fetch(request, env, ctx) {
    // 读取数据
    const value = await env.MY_KV.get('key');
    
    // 写入数据
    await env.MY_KV.put('key', 'value');
    
    // 删除数据
    await env.MY_KV.delete('key');
    
    return Response.json({ value });
  },
};
```

### 🌍 环境变量
```javascript
export default {
  async fetch(request, env, ctx) {
    const apiKey = env.API_KEY;
    const dbUrl = env.DATABASE_URL;
    
    return Response.json({
      environment: env.ENVIRONMENT
    });
  },
};
```

</div>
<div>

### 📡 外部API调用
```javascript
export default {
  async fetch(request, env, ctx) {
    const response = await fetch(
      'https://api.example.com/data',
      {
        headers: {
          'Authorization': `Bearer ${env.API_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const data = await response.json();
    
    return Response.json({
      status: 'success',
      data: data
    });
  },
};
```

### ⏰ 定时任务
```javascript
export default {
  async scheduled(event, env, ctx) {
    // 定时执行的代码
    console.log('Scheduled task running');
    
    // 调用外部API
    await fetch('https://api.example.com/cron');
  },
};
```

</div>
</div>

---

# 📋 配置文件详解

## wrangler.toml 配置

<div class="columns">
<div>

### 🔧 基础配置
```toml
name = "my-worker"
main = "src/index.js"
compatibility_date = "2024-01-01"

[env.production]
name = "my-worker-prod"
vars = { ENVIRONMENT = "production" }

[env.staging]
name = "my-worker-staging"
vars = { ENVIRONMENT = "staging" }
```

</div>
<div>

### 🗄️ KV 绑定
```toml
[[kv_namespaces]]
binding = "MY_KV"
id = "your-kv-namespace-id"
preview_id = "preview-kv-namespace-id"

# 环境变量
[vars]
API_KEY = "your-api-key"
DEBUG = "true"

# 定时任务
[triggers]
crons = ["0 0 * * *"]
```

</div>
</div>

---

# 🚀 部署与管理

<div class="columns">
<div>

### 📤 部署命令
```bash
# 开发环境预览
wrangler dev

# 部署到生产环境
wrangler deploy

# 部署到指定环境
wrangler deploy --env staging

# 查看部署状态
wrangler deployments list
```

### 📊 监控日志
```bash
# 实时查看日志
wrangler tail

# 查看特定环境日志
wrangler tail --env production

# 过滤日志
wrangler tail --format pretty
```

</div>
<div>

### 🔄 版本管理
```bash
# 查看版本历史
wrangler deployments list

# 回滚到指定版本
wrangler rollback [deployment-id]

# 查看当前版本
wrangler whoami
```

### 🛠️ 调试技巧
<div class="feature-box">

- **本地开发**：使用 `wrangler dev`
- **日志调试**：`console.log()` 输出
- **错误处理**：try-catch 包装
- **性能监控**：使用 Analytics

</div>

</div>
</div>

---

# 💡 最佳实践

<div class="columns">
<div>

### 🎯 性能优化
<div class="feature-box">

- **缓存策略**：合理使用Cache API
- **请求合并**：减少外部调用
- **数据压缩**：gzip响应内容
- **CDN利用**：静态资源缓存

</div>

### 🔒 安全考虑
<div class="feature-box">

- **输入验证**：严格验证用户输入
- **CORS设置**：正确配置跨域
- **密钥管理**：使用环境变量
- **访问控制**：IP白名单等

</div>

</div>
<div>

### 📈 监控运维
<div class="feature-box">

- **错误追踪**：集成错误监控
- **性能指标**：响应时间监控
- **使用统计**：请求量分析
- **告警设置**：异常通知

</div>

### 💰 成本控制
<div class="feature-box">

- **请求优化**：减少不必要调用
- **缓存利用**：降低计算成本
- **资源清理**：定期清理数据
- **用量监控**：设置使用限制

</div>

</div>
</div>

---

# 🌟 实际应用案例

## 常见使用场景

<div class="columns">
<div>

### 🔗 API 代理
<div class="quote">
"使用Workers作为API网关，实现请求路由、认证、限流等功能，提升API的可用性和安全性。"
</div>

### 🌐 静态网站托管
<div class="quote">
"结合Workers Sites功能，实现静态网站的全球CDN分发，提供极速的访问体验。"
</div>

</div>
<div>

### 🧪 A/B 测试
<div class="quote">
"在边缘层面实现流量分割，进行A/B测试，无需修改后端代码即可快速验证新功能。"
</div>

### 🛡️ 安全防护
<div class="quote">
"实现WAF规则、DDoS防护、Bot检测等安全功能，在请求到达源服务器前进行过滤。"
</div>

</div>
</div>

---

# 📚 学习资源

<div class="columns">
<div>

### 📖 官方文档
- **开发者文档**：[developers.cloudflare.com](https://developers.cloudflare.com)
- **API参考**：完整的API文档
- **示例代码**：丰富的代码示例
- **最佳实践**：官方推荐做法

### 🎓 学习路径
- **基础概念**：了解边缘计算
- **JavaScript基础**：掌握ES6+语法
- **Web API**：熟悉Fetch、Response等
- **实践项目**：动手构建应用

</div>
<div>

### 🛠️ 开发工具
- **Wrangler CLI**：命令行工具
- **Workers Playground**：在线编辑器
- **VS Code插件**：开发支持
- **调试工具**：日志和监控

### 🤝 社区支持
- **Discord社区**：实时交流
- **GitHub仓库**：开源项目
- **技术博客**：经验分享
- **Stack Overflow**：问题解答

</div>
</div>

---

# 🎉 开始您的 Workers 之旅

<div class="highlight">

**现在就开始使用 Cloudflare Workers 构建您的边缘应用！**

</div>

## 🚀 下一步行动

1. **注册账户**：访问 Cloudflare Dashboard
2. **安装工具**：设置 Wrangler CLI
3. **创建项目**：从 Hello World 开始
4. **部署应用**：发布到全球边缘网络
5. **监控优化**：持续改进性能

<div class="stats">

**准备好了吗？让我们开始边缘计算的精彩旅程！** ✨

</div>
